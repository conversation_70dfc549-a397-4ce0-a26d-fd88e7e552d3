"use client";
import { EllipsisVertical } from "lucide-react";
import React, { useState, useRef } from "react";
import { Avatar, AvatarImage } from "~/components/ui/avatar";
import { Button } from "~/components/ui/button";
import MenuDropdown from "./menu-dropdown";

const ChatHeader = ({ participants }: any) => {
  const [isMenuDropdownOpen, setIsMenuDropdownOpen] = useState(false);
  const menuDropdownRef = useRef<HTMLDivElement>(null);

  //

  return (
    <nav className="flex items-center justify-between p-5 border-b border-[#E6EAEF]">
      <h2 className="text-[#1D2939] text-lg font-bold">
        {participants?.map((user: any) => user.username).join(", ")}
      </h2>

      <div className="flex items-center gap-3">
        {/* avatar badge group */}
        <div className="flex rounded-[5px] border border-[#E6EAEF] p-2 h-9">
          <div className="flex items-center gap-1.5">
            {participants?.map((member: any) => (
              <Avatar
                key={member.user_id}
                className="rounded-[5px] w-5 h-5 -ml-2.5 first:ml-0 border border-[#E6EAEF]"
              >
                <AvatarImage src={member.avatar_url || "/images/user.png"} />
              </Avatar>
            ))}

            {participants?.length > 3 && (
              <span className="text-[13px] font-semibold text-[#344054]">
                +{participants?.length - 3}
              </span>
            )}
          </div>
        </div>

        <div className="relative" ref={menuDropdownRef}>
          <Button
            variant="outline"
            className={`p-2 border-[#E6EAEF] h-9 ${
              isMenuDropdownOpen ? "bg-[#F6F7F9]" : ""
            }`}
            onClick={() => setIsMenuDropdownOpen(!isMenuDropdownOpen)}
          >
            <EllipsisVertical className="w-5 h-5" color="#344054" />
          </Button>
          <MenuDropdown
            isOpen={isMenuDropdownOpen}
            onClose={() => setIsMenuDropdownOpen(false)}
          />
        </div>
      </div>
    </nav>
  );
};

export default ChatHeader;
