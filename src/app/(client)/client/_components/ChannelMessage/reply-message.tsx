import React, { useContext, useState } from "react";
import { SmilePlus, Bookmark } from "lucide-react";
import Image from "next/image";
import images from "~/assets/images";
import MessageItem from "./message-item";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "~/components/ui/popover";
import EmojiPicker from "@emoji-mart/react";
import data from "@emoji-mart/data";
import { DataContext } from "~/store/GlobalState";
import ReplyMore from "./reply-more";

const ReplyMessages = ({ item, shouldShowAvatar }: any) => {
  const [isEmojiPickerOpen, setIsEmojiPickerOpen] = useState(false);
  const { state } = useContext(DataContext);
  const { editor } = state;

  const onEmojiClick = (emojiData: any) => {
    if (editor) {
      editor.chain().focus().insertContent(emojiData?.native).run();
      setIsEmojiPickerOpen(false);
    }
  };

  //

  return (
    <div
      className={`h-[100%] relative bg-white group hover:bg-gray-50 py-1 transition-colors flex items-start px-3`}
    >
      <div className="w-8 mr-2 flex-shrink-0 flex items-center justify-center">
        {shouldShowAvatar ? (
          <div className="w-8 h-8 relative size-9">
            <Image
              src={
                item?.avatar_url
                  ? item?.avatar_url
                  : item?.user_type == "user" || item?.user_type === ""
                    ? images?.user
                    : images?.bot
              }
              alt="avatar"
              width={100}
              height={100}
              objectFit="cover"
              className="rounded-[7px] border size-9 object-cover"
            />
          </div>
        ) : (
          <span className="text-xs text-[#98A2B3] mt-1 opacity-0 group-hover:opacity-100 transition-opacity">
            {new Date(item?.created_at)
              .toLocaleTimeString([], {
                hour: "numeric",
                minute: "2-digit",
                hour12: true,
              })
              .replace(/ am| pm/, "")}
          </span>
        )}
      </div>

      <div>
        {shouldShowAvatar && (
          <div className="flex items-center gap-2">
            <span className="font-bold text-[15px] text-[#1D2939]">
              {item?.username || item?.email}
            </span>

            <span className="text-xs text-[#98A2B3]">
              {new Date(item?.created_at)
                .toLocaleTimeString([], {
                  hour: "numeric",
                  minute: "2-digit",
                  hour12: true,
                })
                .replace(/ AM| PM/, "")}
            </span>
          </div>
        )}

        <div className="relative flex items-start justify-between">
          <div className="flex items-center gap-2">
            <MessageItem item={item} />

            <span className="text-[9px] text-neutral-500">
              {item?.edited ? "(edited)" : ""}
            </span>
          </div>
        </div>

        <div className="opacity-0 group-hover:opacity-100 transition-opacity flex items-center ml-4 absolute right-5 -top-6 z-10 bg-white shadow-md rounded-[8px] border border-[#E6EAEF] p-[2px]">
          <Popover open={isEmojiPickerOpen} onOpenChange={setIsEmojiPickerOpen}>
            <PopoverTrigger asChild>
              <button className="py-[7px] px-[10px] hover:bg-gray-200 rounded">
                <SmilePlus size={18} className="text-[#667085]" />
              </button>
            </PopoverTrigger>
            <PopoverContent
              className="p-0"
              align="end"
              avoidCollisions
              collisionPadding={80}
            >
              <EmojiPicker data={data} onEmojiSelect={onEmojiClick} />
            </PopoverContent>
          </Popover>
          <button className="py-[7px] px-[10px] hover:bg-gray-200 rounded">
            <Bookmark size={18} className="text-[#667085]" />
          </button>
          <ReplyMore item={item} />
        </div>
      </div>
    </div>
  );
};

export default ReplyMessages;
