import React, { useContext, useState } from "react";
import { Forward, MessageCircleMore, SmilePlus, Bookmark } from "lucide-react";
import MessageItem from "./message-item";
import { DataContext } from "~/store/GlobalState";
import { ACTIONS } from "~/store/Actions";
import { usePathname } from "next/navigation";
import ReplyCard from "../reply-card";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "~/components/ui/popover";
import EmojiPicker from "@emoji-mart/react";
import data from "@emoji-mart/data";
import More from "./more";
import UserHoverCard from "../hover-card/user";

//

const Message = ({ item, shouldShowAvatar }: any) => {
  const { state, dispatch } = useContext(DataContext);
  const { editor } = state;
  const pathname = usePathname();
  const [isEmojiPickerOpen, setIsEmojiPickerOpen] = useState(false);

  const handleReply = () => {
    dispatch({ type: ACTIONS.THREAD, payload: item });
    dispatch({ type: ACTIONS.REPLY, payload: true });
  };

  const onEmojiClick = (emojiData: any) => {
    if (editor) {
      editor.chain().focus().insertContent(emojiData?.native).run();
      setIsEmojiPickerOpen(false);
    } else {
      console.log("editor not available");
    }
  };

  const handleOpen = () => {
    dispatch({ type: ACTIONS.USER_DATA, payload: item });
    dispatch({ type: ACTIONS.HOVER_PROFILE, payload: true });
  };

  //

  return (
    <div
      className={`relative bg-white group hover:bg-gray-50 py-1 transition-colors flex items-start px-5 gap-2`}
    >
      <div className="w-flex items-center justify-center">
        {shouldShowAvatar ? (
          <UserHoverCard item={item} handleOpen={handleOpen} />
        ) : (
          <span className="block text-xs w-[36px] text-[#98A2B3] mt-1 opacity-0 group-hover:opacity-100 transition-opacity">
            {new Date(item?.created_at)
              .toLocaleTimeString([], {
                hour: "numeric",
                minute: "2-digit",
                hour12: true,
              })
              .replace(/\s?(am|pm)/i, "")}
          </span>
        )}
      </div>

      <div>
        {shouldShowAvatar && (
          <div className="flex items-center gap-2">
            <span
              className="hover font-bold text-[15px] text-[#1D2939] cursor"
              onClick={handleOpen}
            >
              {item?.username || item?.email}
            </span>

            <span className="text-xs text-[#98A2B3] mt-[1px]">
              {new Date(item?.created_at)
                .toLocaleTimeString([], {
                  hour: "numeric",
                  minute: "2-digit",
                  hour12: true,
                })
                .replace(/\s?(am|pm)/i, "")}
            </span>
          </div>
        )}

        <div className="relative flex items-start justify-between">
          <div className="flex items-center gap-2">
            <MessageItem item={item} />

            <div className="text-[9px] text-neutral-500 mt-[2px]">
              {item?.edited ? "(edited)" : ""}
            </div>
          </div>
        </div>

        {item?.message_count > 0 && (
          <div className="">
            <ReplyCard
              users={item?.messages}
              totalReplies={item?.message_count}
              lastReplyTime={item.last_reply || item?.created_at}
              handleReply={handleReply}
            />
          </div>
        )}

        {item?.type !== "system" && (
          <div className="opacity-0 group-hover:opacity-100 transition-opacity flex items-center ml-4 absolute right-5 -top-6 z-10 bg-white shadow-md rounded-[8px] border border-[#E6EAEF] p-[2px]">
            <Popover
              open={isEmojiPickerOpen}
              onOpenChange={setIsEmojiPickerOpen}
            >
              <PopoverTrigger asChild>
                <button className="py-[7px] px-[10px] hover:bg-gray-200 rounded">
                  <SmilePlus size={18} className="text-[#667085]" />
                </button>
              </PopoverTrigger>
              <PopoverContent className="p-0 w-full max-w-xs" align="end">
                <EmojiPicker data={data} onEmojiSelect={onEmojiClick} />
              </PopoverContent>
            </Popover>

            {!pathname?.includes("/agents") && (
              <button
                className="py-[7px] px-[10px] hover:bg-gray-200 rounded"
                onClick={handleReply}
              >
                <MessageCircleMore size={18} className="text-[#667085]" />
              </button>
            )}

            <button className="py-[7px] px-[10px] hover:bg-gray-200 rounded">
              <Forward size={18} className="text-[#667085]" />
            </button>
            <button className="py-[7px] px-[10px] hover:bg-gray-200 rounded">
              <Bookmark size={18} className="text-[#667085]" />
            </button>
            <More item={item} />
          </div>
        )}
      </div>
    </div>
  );
};

export default Message;
