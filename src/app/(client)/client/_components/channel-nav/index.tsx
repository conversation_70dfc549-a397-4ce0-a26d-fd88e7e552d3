"use client";
import { <PERSON>lip<PERSON>Vertical, PlusIcon } from "lucide-react";
import React, { useState, useRef, useEffect, useContext } from "react";
import { Avatar, AvatarImage } from "~/components/ui/avatar";
import { But<PERSON> } from "~/components/ui/button";
import AgentDropdown from "./agent-dropdown";
import MenuDropdown from "./menu-dropdown";
import { DataContext } from "~/store/GlobalState";
import { GetRequest, PostRequest } from "~/utils/new-request";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "~/components/ui/dialog";
import { Input } from "~/components/ui/input";
import Loading from "~/components/ui/loading";
import cogoToast from "cogo-toast";
import { useParams } from "next/navigation";
import { ACTIONS } from "~/store/Actions";
import ChannelDetailsDialog from "../channel-details-dialog";

const ChannelHeader = () => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isMenuDropdownOpen, setIsMenuDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const menuDropdownRef = useRef<HTMLDivElement>(null);
  const { state, dispatch } = useContext(DataContext);
  const { channelDetails, agentModal } = state;
  const [agents, setAgents] = useState<any>([]);
  const [url, setUrl] = useState("");
  const [createloading, setCreateloading] = useState(false);
  // const { state, dispatch } = useContext(DataContext);
  const [callback, setCallback] = useState(false);
  const params = useParams();
  const channelId = params.id as string;

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // get agents in an organisation
  useEffect(() => {
    const getAgents = async () => {
      const res = await GetRequest(
        `/organisations/${state?.orgId}/channels/${channelId}/agents`
      );
      if (res?.status === 200 || res?.status === 201) {
        setAgents(res?.data?.data);
        dispatch({ type: ACTIONS.CHANNEL_AGENTS, payload: res?.data?.data });
      }
    };
    getAgents();
  }, [callback, state?.orgId, channelId]);

  // handle save
  const handleSave = async (e: any) => {
    e.preventDefault();

    const orgId = localStorage.getItem("orgId") || "";

    setCreateloading(true);

    const payload = {
      json_url: url,
    };

    const res = await PostRequest(`/organisations/${orgId}/agents`, payload);
    if (res?.status === 200 || res?.status === 201) {
      setCallback(!callback);
      dispatch({
        type: ACTIONS.AGENT_CALLBACK,
        payload: !state?.agentCallback,
      });
      cogoToast.success(res?.data?.message);
      setTimeout(() => {
        dispatch({ type: ACTIONS.AGENT_MODAL, payload: false });
      }, 1000);
    }
    setCreateloading(false);
  };

  // show modal
  const handleModal = () => {
    dispatch({ type: ACTIONS.AGENT_MODAL, payload: true });
    setIsDropdownOpen(false);
  };

  //

  return (
    <nav className="flex items-center justify-between p-5 border-b border-[#E6EAEF]">
      <ChannelDetailsDialog>
        <h2 className="text-lg font-bold"># {channelDetails?.name}</h2>
      </ChannelDetailsDialog>

      {!state?.channelloading &&
        state?.channelDetails?.access === true &&
        !state?.channelDetails?.archived && (
          <div className="flex items-center gap-3">
            <div className="relative" ref={dropdownRef}>
              <Button
                variant="outline"
                className="border-blue-50 h-9"
                onClick={() => setIsDropdownOpen(!isDropdownOpen)}
              >
                <PlusIcon className="w-5 h-5" color="#8686F9" />
                <span className="ml-1 text-[13px] font-semibold text-blue-200">
                  Add an Agent
                </span>
              </Button>

              <AgentDropdown
                isOpen={isDropdownOpen}
                agents={agents}
                showModal={handleModal}
                setCallback={setCallback}
                callback={callback}
              />
            </div>

            {/* Add agent json */}
            <Dialog
              open={agentModal}
              onOpenChange={() =>
                dispatch({ type: ACTIONS.AGENT_MODAL, payload: agentModal })
              }
            >
              <DialogContent className="w-full max-w-md">
                <DialogHeader className="mb-5">
                  <DialogTitle className="text-blue-500">
                    Provide your Agent Json url
                  </DialogTitle>
                </DialogHeader>

                <div className="flex flex-col gap-2">
                  <label className="text-sm font-medium">Json url</label>
                  <Input
                    value={url}
                    onChange={(e) => setUrl(e.target.value)}
                    placeholder="Enter json url"
                  />
                </div>

                <DialogFooter className="mt-4 flex justify-end gap-2">
                  <Button
                    variant="outline"
                    onClick={() =>
                      dispatch({ type: ACTIONS.AGENT_MODAL, payload: false })
                    }
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleSave}
                    disabled={!url}
                    className="bg-blue-500 gap-1 text-white px-8"
                  >
                    Save
                    {createloading && <Loading />}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            <div className="w-px h-5 bg-[#E6EAEF]" />

            {/* avatar badge group */}
            <ChannelDetailsDialog>
              <div
                onClick={() =>
                  dispatch({ type: ACTIONS.ACTIVE_TAB, payload: "people" })
                }
                className="flex rounded-[5px] border border-[#E6EAEF] p-2 h-9 cursor-pointer"
              >
                <div className="flex items-center gap-1.5">
                  {channelDetails?.users
                    ?.slice(0, 3)
                    .map((member: any, index: number) => (
                      <Avatar
                        key={member.id}
                        className={`rounded-[5px] w-5 h-5 border border-[#E6EAEF] ${
                          index > 0 ? "-ml-2.5" : ""
                        }`}
                      >
                        <AvatarImage
                          src={
                            member?.profile?.avatar_url || "/images/user.png"
                          }
                        />
                      </Avatar>
                    ))}

                  {channelDetails?.users?.length > 3 && (
                    <span className="text-[13px] font-semibold text-[#344054]">
                      +{channelDetails.users.length - 3}
                    </span>
                  )}
                </div>
              </div>
            </ChannelDetailsDialog>

            <div className="relative" ref={menuDropdownRef}>
              <Button
                variant="outline"
                className={`p-2 border-[#E6EAEF] h-9 ${
                  isMenuDropdownOpen ? "bg-[#F6F7F9]" : ""
                }`}
                onClick={() => setIsMenuDropdownOpen(!isMenuDropdownOpen)}
              >
                <EllipsisVertical className="w-5 h-5" color="#344054" />
              </Button>

              <MenuDropdown
                isOpen={isMenuDropdownOpen}
                onClose={() => setIsMenuDropdownOpen(false)}
              />
            </div>
          </div>
        )}
    </nav>
  );
};

export default ChannelHeader;
