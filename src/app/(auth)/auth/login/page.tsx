"use client";

import Link from "next/link";
import { Checkbox } from "~/components/ui/checkbox";
import Image from "next/image";
import React, { useState } from "react";
import { But<PERSON> } from "~/components/ui/button";
import { useRouter } from "next/navigation";
import { Toaster } from "~/components/ui/toaster";
import { PostRequest } from "~/utils/request";
import Loading from "~/components/ui/loading";
import GoogleLogin from "react-google-login";
import { WebhookRequest } from "~/utils/webhook-request";

//

function Login() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [errors, setErrors] = useState({ email: "", password: "" });
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [googleloading, setGoogleloading] = useState(false);

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const validateForm = () => {
    const newErrors = { email: "", password: "" };
    if (!email) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = "Invalid email address";
    }

    if (!password) {
      newErrors.password = "Password is required";
    } else if (password.length < 6) {
      newErrors.password = "Password must be at least 6 characters";
    }

    setErrors(newErrors);
    return !newErrors.email && !newErrors.password;
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (validateForm()) {
      setLoading(true);

      const payload = {
        email,
        password,
      };

      const route = localStorage.getItem("route") || "";

      const res = await PostRequest("/auth/login", payload);

      if (res?.status === 200 || res?.status === 201) {
        localStorage.setItem("token", res?.data?.data?.access_token);
        localStorage.setItem("useremail", res?.data?.data?.user?.email);
        localStorage.setItem("user", JSON.stringify(res?.data?.data?.user));
        localStorage.setItem("orgId", res?.data?.data?.user?.current_org);

        if (route) {
          setTimeout(() => {
            router.push(route);
          }, 100);
        } else {
          setTimeout(() => {
            if (res?.data?.data?.user?.is_onboarded) {
              router.push(`/client`);
            } else {
              router.push("/client/welcome");
            }
          }, 100);
        }

        // after successful login call this endpoint
        WebhookRequest(
          res?.data?.data?.user?.username,
          "New User Login",
          res?.data?.message,
          "success"
        );
      } else {
        setLoading(false);
        WebhookRequest(
          email,
          "New User Login",
          res?.response?.data?.message,
          "error"
        );
      }
    }
  };

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
    setErrors((prevErrors) => ({ ...prevErrors, email: "" }));
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPassword(e.target.value);
    setErrors((prevErrors) => ({ ...prevErrors, password: "" }));
  };

  const onSuccess = async (response: any) => {
    setGoogleloading(true);

    if (response?.tokenId) {
      const res = await PostRequest("/auth/google", {
        id_token: response?.tokenId,
      });

      if (res?.status === 200 || res?.status === 201) {
        // check if the user is a new user or existing user

        localStorage.setItem("token", res?.data?.data?.access_token);
        localStorage.setItem("useremail", res?.data?.data?.user?.email);
        localStorage.setItem("user", JSON.stringify(res?.data?.data?.user));
        localStorage.setItem("orgId", res?.data?.data?.user?.current_org);

        if (res?.data?.data?.user?.is_onboarded) {
          router.push(`/client`);
        } else {
          router.push("/client/welcome");
        }

        setGoogleloading(false);

        // after successful login call this endpoint
        WebhookRequest(
          res?.data?.data?.user?.username,
          "New User Login",
          res?.data?.message,
          "success"
        );
      } else {
        setGoogleloading(false);
      }
    }
  };

  //

  return (
    <>
      <Toaster />
      <main className="w-full min-h-screen flex flex-col md:flex-row">
        <section className="w-full md:w-[45%] bg-[#F9FAFB] px-[20px] md:px-[40px] py-[20px] hidden lg:block">
          <Link href="/">
            <Image src="/login_logo.svg" alt="" width={86} height={31} />
          </Link>
          <div>
            <div className="w-full flex flex-col gap-[12px] mb-[40px] md:mb-[71px]">
              <h1 className="w-full text-center mt-[40px] md:mt-[70px] text-[24px] md:text-[28px] font-[600] leading-[30px] md:leading-[35px]">
                All Your{" "}
                <span className="text-[24px] md:text-[28px] font-[600] leading-[30px] md:leading-[35px] bg-gradient-to-t from-[#8860F8] to-[#7141F8] bg-clip-text text-transparent">
                  Notifications
                </span>{" "}
                In One App!!!
              </h1>
              <p className="w-full text-center text-[12px] md:text-[14px] text-[#344054] font-[400] leading-[18px] md:leading-[21px] px-[20px] md:px-[90px]">
                Your Central Hub for Real-Time Notifications, Events, and Errors
                – Stay Connected to Your Infrastructure, Databases, and Servers
                with Instant Updates.
              </p>
            </div>
            <Image
              src="/login_img.png"
              alt="loginImg"
              width={320}
              height={320}
              className="flex justify-center items-center mx-auto md:w-[420px] md:h-[420px]"
            />
          </div>
        </section>
        <section className="w-full md:w-[55%] flex flex-col max-w-xs md:max-w-lg mx-auto items-start justify-start pt-[20px] md:pt-0">
          <Link href="/">
            <Image
              src="/logomobile.svg"
              alt="logo_mobile"
              width={86}
              height={31}
              className="sm:block md:block lg:hidden flex"
            />
          </Link>
          <div className="w-full flex flex-col justify-center mt-[60px] md:mt-[80px] items-center gap-[8px] mb-[32px]">
            <h1 className="w-full text-center text-[24px] md:text-[28px] font-[600] leading-[30px] md:leading-[35px]">
              Login to Telex
            </h1>
            <p className="w-full text-center text-[14px] md:text-[16px] text-[#344054] font-[400] leading-[21px] md:leading-[27px]">
              Welcome back! We&apos;ve missed you!
            </p>
          </div>
          <form onSubmit={handleSubmit} className="w-full">
            <div className="flex flex-col gap-[16px]">
              <div className="w-full flex flex-col gap-[8px] relative">
                <label
                  htmlFor="email"
                  className="text-[14px] font-[400] leading-[21px]"
                >
                  Email address
                </label>
                <div className="w-full flex flex-col gap-[2px]">
                  <input
                    type="text"
                    value={email}
                    onChange={handleEmailChange}
                    placeholder="Enter your email"
                    className={`w-full text-[14px] text-[#667085] leading-[15.12px] font-[500] h-[48px] border ${
                      errors.email ? "border-[#F81404]" : "border-[#D0D0FD]"
                    } outline-none rounded-md py-[13px] pl-[13px]`}
                  />
                  {errors.email && (
                    <small className="text-[12px] text-[#F81404]">
                      {errors.email}
                    </small>
                  )}
                </div>
              </div>
              <div className="w-full flex flex-col gap-[8px] relative">
                <label
                  htmlFor="password"
                  className="text-[14px] font-[400] leading-[21px]"
                >
                  Password
                </label>
                <div className="w-full flex flex-col gap-[2px]">
                  <div className="relative">
                    <input
                      type={showPassword ? "text" : "password"}
                      value={password}
                      onChange={handlePasswordChange}
                      placeholder="Password"
                      className={`w-full text-[14px] text-[#667085] leading-[15.12px] font-[500] h-[48px] border ${
                        errors.password
                          ? "border-[#F81404]"
                          : "border-[#D0D0FD]"
                      } outline-none rounded-md py-[13px] pl-[13px] pr-[40px]`}
                    />
                    <button
                      type="button"
                      onClick={togglePasswordVisibility}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 focus:outline-none"
                    >
                      <Image
                        src={showPassword ? "/eye_closed.svg" : "/eye_open.svg"}
                        alt={showPassword ? "Hide password" : "Show password"}
                        width={20}
                        height={20}
                      />
                    </button>
                  </div>
                  {errors.password && (
                    <small className="text-[12px] text-[#F81404]">
                      {errors.password}
                    </small>
                  )}
                </div>
              </div>
            </div>
            <div className="mt-[10px] flex justify-between mb-[32px]">
              <div className="flex flex-row gap-[4px] items-center">
                <Checkbox />
                <p className="text-[14px] font-[500] leading-[17.64px]">
                  Remember me
                </p>
              </div>
              <Link href="/auth/forgot-password">
                <p className="text-[14px] font-[500] leading-[21px] hover:text-[#7141F8]">
                  Forgot Password?
                </p>
              </Link>
            </div>

            <div className="flex flex-col gap-[24px]">
              <div className="flex flex-col gap-[16px]">
                <Button
                  type="submit"
                  variant="default"
                  className="py-6 bg-[#7141F8] hover:bg-[#8760f8] text-white"
                  disabled={googleloading ? true : false}
                >
                  {loading ? (
                    <span className="flex items-center gap-x-2">
                      <span className="animate-pulse">Logging in...</span>{" "}
                      <Loading width="20" height="40" />
                    </span>
                  ) : (
                    <span>Login</span>
                  )}
                </Button>

                <Link href="/auth/magiclink">
                  <Button
                    type="submit"
                    variant="outline"
                    className="w-full py-6 border border-[#8760f8] text-[#8760f8] bg-white hover:bg-[#7141F8] hover:text-white"
                  >
                    Login with magic link
                  </Button>
                </Link>
              </div>

              <div className="flex flex-col gap-[10px]">
                <div className="border border-[#D0D0FD] rounded-md">
                  <GoogleLogin
                    clientId={process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID || ""}
                    render={(renderProps) => (
                      <div
                        onClick={renderProps.onClick}
                        className="cursor-pointer flex flex-row gap-[10px] border border-[#D0D0FD] rounded-md justify-center py-[11px]"
                      >
                        <Image
                          src="/google.svg"
                          width={24}
                          height={24}
                          alt="google"
                        />
                        <div className="text-[16px] font-[600] leading-[20.16px]">
                          {googleloading ? (
                            <span className="flex items-center gap-x-2">
                              <span className="animate-pulse">
                                Logging in...
                              </span>{" "}
                              <Loading width="20" height="20" color="#7141F8" />
                            </span>
                          ) : (
                            "Sign in with Google"
                          )}
                        </div>
                      </div>
                    )}
                    buttonText="Sign in with Google"
                    className="w-full flex align-center justify-center text-md shadow-none"
                    onSuccess={onSuccess}
                    cookiePolicy={"single_host_origin"}
                    prompt="login"
                  />
                </div>
              </div>

              <div className="flex justify-center items-center">
                <p className="text-[14px] font-[400] leading-[21px]">
                  Don’t have an account?{" "}
                  <Link href="/auth/sign-up">
                    <span className="text-[14px] font-[500] leading-[21px] text-[#7141F8] hover:text-[#0A0A0A]">
                      Sign up
                    </span>
                  </Link>
                </p>
              </div>
            </div>
          </form>
        </section>
      </main>
    </>
  );
}

export default Login;
