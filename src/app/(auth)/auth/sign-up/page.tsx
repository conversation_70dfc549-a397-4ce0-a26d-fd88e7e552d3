"use client";

import Link from "next/link";
import Image from "next/image";
import React, { useState, useEffect } from "react";
import { Button } from "~/components/ui/button";
import { useRouter } from "next/navigation";
import { Checkbox } from "~/components/ui/checkbox";
import Loading from "~/components/ui/loading";
import { PostRequest } from "~/utils/request";
import GoogleLogin from "react-google-login";

function SignUp() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [formSubmitted, setFormSubmitted] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [passwordFocused, setPasswordFocused] = useState(false);
  const [errors, setErrors] = useState({
    email: "",
    password: "",
  });
  const router = useRouter();

  const [isMinLength, setIsMinLength] = useState(false);
  const [isValidPassword, setIsValidPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [googleloading, setGoogleloading] = useState(false);

  useEffect(() => {
    const fromInvite = new URLSearchParams(window.location.search).get(
      "invite"
    );

    if (fromInvite) {
      const fullUrl = window.location.href;
      localStorage.setItem("postInviteRedirect", fullUrl);
    }
  }, []);

  useEffect(() => {
    const minLength = password.length >= 6;
    setIsMinLength(minLength);
    setIsValidPassword(minLength);
    setFormSubmitted(false);
  }, [password]);

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const handleFocus = () => {
    setPasswordFocused(true);
  };

  const handleBlur = () => {
    setPasswordFocused(false);
  };

  // validate form
  const validateForm = () => {
    const newErrors = { email: "", password: "" };

    if (!email || email.trim() === "") {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = "Invalid email address";
    }

    if (!password || password.trim() === "") {
      newErrors.password = "Password is required";
    } else if (!isValidPassword) {
      newErrors.password = "Password is not valid";
    } else if (!isMinLength) {
      newErrors.password = "Password must have a minimum of 6 characters";
    }

    setErrors(newErrors);
    return !newErrors.email && !newErrors.password;
  };

  // email change
  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
    if (errors.email) {
      setErrors((prevErrors) => ({ ...prevErrors, email: "" }));
    }
  };

  // password change
  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPassword(e.target.value);
    if (errors.password) {
      setErrors((prevErrors) => ({ ...prevErrors, password: "" }));
    }
  };

  // handle submit
  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (validateForm() && isValidPassword) {
      setLoading(true);
      const payload = {
        email,
        password,
      };

      const res = await PostRequest("/auth/register", payload);

      if (res?.status === 200 || res?.status === 201) {
        localStorage.setItem("token", res?.data?.data?.access_token);
        localStorage.setItem("useremail", res?.data?.data?.user?.email);
        localStorage.setItem("user", JSON.stringify(res?.data?.data?.user));
        localStorage.setItem("orgId", res?.data?.data?.user?.current_org);

        setTimeout(() => {
          if (res?.data?.data?.user?.is_onboarded) {
            router.push(`/client`);
          } else {
            router.push("/client/welcome");
          }
        }, 100);
      } else {
        setLoading(false);
      }
    }
  };

  const onSuccess = async (response: any) => {
    setGoogleloading(true);
    if (response?.tokenId) {
      const res = await PostRequest("/auth/google", {
        id_token: response?.tokenId,
      });

      if (res?.status === 200 || res?.status === 201) {
        localStorage.setItem("token", res?.data?.data?.access_token);
        localStorage.setItem("useremail", res?.data?.data?.user?.email);
        localStorage.setItem("user", JSON.stringify(res?.data?.data?.user));
        localStorage.setItem("orgId", res?.data?.data?.user?.current_org);

        // check if the user is a new user or existing user

        if (res?.data?.data?.user?.is_onboarded) {
          router.push(`/client`);
        } else {
          router.push("/client/welcome");
        }

        setGoogleloading(false);
      } else {
        setGoogleloading(false);
      }
    }
  };

  //

  return (
    <main className="w-full min-h-screen flex flex-col md:flex-row">
      <section className="w-full md:w-[45%] bg-[#F9FAFB] px-[20px] md:px-[40px] py-[20px] hidden lg:block">
        <Link href="/">
          <Image src="/login_logo.svg" alt="" width={86} height={31} />
        </Link>
        <div>
          <div className="w-full flex flex-col gap-[12px] mb-[40px] md:mb-[71px]">
            <h1 className="w-full text-center mt-[40px] md:mt-[70px] text-[24px] md:text-[28px] font-[600] leading-[30px] md:leading-[35px]">
              All Your{" "}
              <span className="text-[24px] md:text-[28px] font-[600] leading-[30px] md:leading-[35px] bg-gradient-to-t from-[#8860F8] to-[#7141F8] bg-clip-text text-transparent">
                Notifications
              </span>{" "}
              In One App!!!
            </h1>
            <p className="w-full text-center text-[12px] md:text-[14px] text-[#344054] font-[400] leading-[21px] md:leading-[27px] px-[20px] md:px-[90px]">
              Your Central Hub for Real-Time Notifications, Events, and Errors –
              Stay Connected to Your Infrastructure, Databases, and Servers with
              Instant Updates.
            </p>
          </div>
          <Image
            src="/login_img.png"
            alt="loginImg"
            width={320}
            height={320}
            className="flex justify-center items-center mx-auto md:w-[420px] md:h-[420px]"
          />
        </div>
      </section>

      <section className="w-full md:w-[55%] flex flex-col max-w-xs md:max-w-lg mx-auto items-start justify-start pt-[20px] md:pt-0 ">
        <Link href="/">
          <Image
            src="/logomobile.svg"
            alt="logo_mobile"
            width={86}
            height={31}
            className="sm:block md:block lg:hidden flex"
          />
        </Link>

        <div className="w-full flex flex-col justify-center mt-[60px] md:mt-[80px] items-center gap-[8px] mb-[32px]">
          <h1 className="w-full text-center text-[24px] md:text-[28px] font-[600] leading-[30px] md:leading-[35px]">
            Create A Telex Account
          </h1>
          <p className="w-full text-center text-[14px] md:text-[16px] text-[#344054] font-[400] leading-[19px] md:leading-[26px] px-[65px]">
            Welcome! Let&apos;s get your profile set up in just a minute.
          </p>
        </div>

        <form onSubmit={handleSubmit} className="w-full pb-[40px]">
          <div className="flex flex-col gap-[16px] mb-[16px]">
            <div className="w-full flex flex-col gap-[8px] relative">
              <label
                htmlFor="email"
                className="text-[14px] font-[400] leading-[21px]"
              >
                Email address
              </label>
              <div className="w-full flex flex-col gap-[2px]">
                <input
                  type="email"
                  value={email}
                  onChange={handleEmailChange}
                  placeholder="Enter your email"
                  className={`w-full text-[14px] text-[#667085] leading-[15.12px] font-[500] h-[48px] border ${
                    errors.email ? "border-[#F81404]" : "border-[#D0D0FD]"
                  } outline-none rounded-md py-[13px] pl-[13px]`}
                />
                {errors.email && (
                  <small className="text-[12px] text-[#F81404]">
                    {errors.email}
                  </small>
                )}
              </div>
            </div>
            <div className="w-full flex flex-col gap-[8px] relative">
              <label
                htmlFor="password"
                className="text-[14px] font-[400] leading-[21px]"
              >
                Password
              </label>
              <div className="w-full flex flex-col gap-[2px]">
                <div className="relative ">
                  <input
                    type={showPassword ? "text" : "password"}
                    value={password}
                    onChange={handlePasswordChange}
                    placeholder="Password"
                    onFocus={handleFocus}
                    onBlur={handleBlur}
                    className={`w-full text-[14px] text-[#667085] leading-[15.12px] font-[500] h-[48px] border ${
                      formSubmitted
                        ? errors.password
                          ? "border-[#F81404]"
                          : "border-[#D0D0FD]"
                        : passwordFocused
                          ? errors.password
                            ? "border-[#F81404]"
                            : "border-[#D0D0FD]"
                          : "border-[#D0D0FD]"
                    } outline-none rounded-md py-[13px] pl-[13px] pr-[10px]`}
                  />
                  <button
                    type="button"
                    onClick={togglePasswordVisibility}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 focus:outline-none"
                  >
                    <Image
                      src={showPassword ? "/eye_closed.svg" : "/eye_open.svg"}
                      alt={showPassword ? "Hide password" : "Show password"}
                      width={20}
                      height={20}
                    />
                  </button>
                </div>

                {errors.password && (
                  <small className="text-[12px] text-[#F81404]">
                    {errors.password}
                  </small>
                )}
              </div>
            </div>
          </div>
          <div className="flex flex-col gap-[24px]">
            <div className="flex flex-col gap-[16px]">
              <Button
                type="submit"
                variant="default"
                className="py-6 bg-[#7141F8] hover:bg-[#8760f8] text-white"
              >
                {loading ? (
                  <span className="flex items-center gap-x-2">
                    <span className="animate-pulse">Creating...</span>{" "}
                    <Loading width="20" height="20" />
                  </span>
                ) : (
                  <span>Create Account</span>
                )}
              </Button>
            </div>

            <div className="flex flex-col gap-[12px]">
              <div className="flex flex-row gap-[6px] items-center">
                <Checkbox className="mt-[2px]" />
                <p className="text-[14px] font-[400] leading-[17.64px]">
                  Stay Signed In
                </p>
              </div>
            </div>

            <div className="border border-[#D0D0FD] rounded-md">
              <GoogleLogin
                clientId={process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID || ""}
                render={(renderProps) => (
                  <div
                    onClick={renderProps.onClick}
                    className="cursor-pointer flex flex-row gap-[10px] border border-[#D0D0FD] rounded-md justify-center py-[11px]"
                  >
                    <Image
                      src="/google.svg"
                      width={24}
                      height={24}
                      alt="google"
                    />
                    <div className="text-[16px] font-[600] leading-[20.16px]">
                      {googleloading ? (
                        <span className="flex items-center gap-x-2">
                          <span className="animate-pulse">Logging in...</span>{" "}
                          <Loading width="20" height="20" color="#7141F8" />
                        </span>
                      ) : (
                        "Sign up with Google"
                      )}
                    </div>
                  </div>
                )}
                buttonText="Sign in with Google"
                className="w-full flex align-center justify-center text-md shadow-none"
                onSuccess={onSuccess}
                cookiePolicy={"single_host_origin"}
                prompt="login"
              />
            </div>
          </div>

          <p className="text-[14px] font-[400] leading-[17.64px] text-center m-2">
            By Signing up, you agree to our
            <Link
              href="/terms-of-service"
              target="_blank"
              className="px-1 font-[500] leading-[21px] text-[#7141F8] hover:text-[#9678e8]"
            >
              terms of service
            </Link>
            and
            <Link
              href="/policy"
              target="_blank"
              className="px-1 font-[500] leading-[21px] text-[#7141F8] hover:text-[#9678e8]"
            >
              privacy policy
            </Link>
          </p>

          <div className="w-full flex justify-center items-center mt-[16px]">
            <p className="text-[14px] font-[400] leading-[17.64px] text-center">
              Already have an account?{" "}
              <Link
                href="/auth/login"
                className="font-[500] leading-[21px] text-[#7141F8] hover:text-[#0A0A0A]"
              >
                Login
              </Link>
            </p>
          </div>
        </form>
      </section>
    </main>
  );
}

export default SignUp;
