name: Deploy Telex Frontend - main

on:
  workflow_dispatch:
  push:
    branches:
      - main

jobs:
  deploy_telex_fe_main:
    runs-on: ubuntu-latest
    if: github.event.repository.fork == false

    steps:
      - name: Checkout repo
        uses: actions/checkout@v4
        
      - name: Build on workflow first
        run: npm install -g pnpm && pnpm install && pnpm build
        
      - name: Rebuild frontend (failsafe)
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.TELEX_SERVER_SSH_HOST }}
          username: ${{ secrets.TELEX_SERVER_SSH_USER }}
          password: ${{ secrets.TELEX_SERVER_SSH_PASSWORD }}
          script: |
            cd /var/www/telex_fe
            git reset --hard
            git pull origin main
            pnpm install
            pnpm build

      - name: Restart App
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.TELEX_SERVER_SSH_HOST }}
          username: ${{ secrets.TELEX_SERVER_SSH_USER }}
          password: ${{ secrets.TELEX_SERVER_SSH_PASSWORD }}
          script: |
            supervisorctl restart telexfe

  release:
    runs-on: ubuntu-latest
    timeout-minutes: 10
    needs: deploy_telex_fe_main
    permissions:
      contents: write
      issues: write
      pull-requests: write
      id-token: write

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "lts/*"

      - name: Install pnpm
        run: |
          npm install -g pnpm
          pnpm install

      - name: Install depedencies
        run: pnpm install --no-frozen-lockfile

      - name: Install semantic-release and plugins
        run: pnpm add semantic-release @semantic-release/git @semantic-release/github --save-dev

      - name: Release
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: pnpx semantic-release
